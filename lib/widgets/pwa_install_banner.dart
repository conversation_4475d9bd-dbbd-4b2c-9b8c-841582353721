import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/simple_pwa_service.dart';
import '../design_system/kft_design_system.dart';
import 'simple_pwa_dialog.dart';

/// Subtle banner that appears at the top to notify users about PWA installation
/// Provides a non-intrusive way to promote app installation
class PWAInstallBanner extends StatefulWidget {
  const PWAInstallBanner({Key? key}) : super(key: key);

  @override
  State<PWAInstallBanner> createState() => _PWAInstallBannerState();
}

class _PWAInstallBannerState extends State<PWAInstallBanner>
    with SingleTickerProviderStateMixin {
  final SimplePWAService _pwaService = SimplePWAService();
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  bool _isVisible = false;
  bool _isDismissed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, -1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    if (kIsWeb) {
      _setupBannerLogic();
    }
  }

  void _setupBannerLogic() {
    // Listen for installability changes
    _pwaService.installPromptAvailable.addListener(_handleInstallabilityChange);
    
    // Check initial state
    _checkShouldShowBanner();
  }

  void _handleInstallabilityChange() {
    _checkShouldShowBanner();
  }

  void _checkShouldShowBanner() {
    if (!mounted || _isDismissed) return;
    
    final shouldShow = _pwaService.installPromptAvailable.value && 
                      !_pwaService.isInstalled;
    
    if (shouldShow && !_isVisible) {
      setState(() {
        _isVisible = true;
      });
      _animationController.forward();
    } else if (!shouldShow && _isVisible) {
      _hideBanner();
    }
  }

  void _hideBanner() {
    _animationController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _isVisible = false;
        });
      }
    });
  }

  void _dismissBanner() {
    setState(() {
      _isDismissed = true;
    });
    _hideBanner();
  }

  Future<void> _handleInstall() async {
    final success = await _pwaService.promptInstall();
    if (!success && mounted) {
      // Show dialog if native prompt failed
      SimplePWADialog.show(
        context,
        onInstallSuccess: () {
          debugPrint('🎉 PWA installation successful from banner');
        },
        onDismiss: () {
          debugPrint('📋 PWA dialog dismissed from banner');
        },
      );
    }
  }

  @override
  void dispose() {
    _pwaService.installPromptAvailable.removeListener(_handleInstallabilityChange);
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!kIsWeb || !_isVisible) {
      return const SizedBox.shrink();
    }

    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              KFTDesignSystem.primaryColor.withOpacity(0.9),
              KFTDesignSystem.primaryColor.withOpacity(0.7),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: KFTDesignSystem.primaryColor.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: SafeArea(
          bottom: false,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: KFTDesignSystem.spacingMd,
              vertical: KFTDesignSystem.spacingMd,
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: const Icon(
                    Icons.download_rounded,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
                const SizedBox(width: KFTDesignSystem.spacingMd),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Install KFT Fitness',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 13,
                        ),
                      ),
                      Text(
                        'Get faster access to your workouts',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 11,
                        ),
                      ),
                    ],
                  ),
                ),
                TextButton(
                  onPressed: _handleInstall,
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: const Text(
                    'Install',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: _dismissBanner,
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 18,
                  ),
                  padding: const EdgeInsets.all(4),
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
