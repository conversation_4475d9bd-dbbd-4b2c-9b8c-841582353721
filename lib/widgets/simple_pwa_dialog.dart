import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/simple_pwa_service.dart';
import '../design_system/kft_design_system.dart';

/// Simple, professional PWA install dialog
/// Shows a clean, professional prompt for PWA installation
class SimplePWADialog extends StatefulWidget {
  final VoidCallback? onInstallSuccess;
  final VoidCallback? onDismiss;

  const SimplePWADialog({
    Key? key,
    this.onInstallSuccess,
    this.onDismiss,
  }) : super(key: key);

  /// Show the simple PWA install dialog
  static Future<void> show(
    BuildContext context, {
    VoidCallback? onInstallSuccess,
    VoidCallback? onDismiss,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (context) => SimplePWADialog(
        onInstallSuccess: onInstallSuccess,
        onDismiss: onDismiss,
      ),
    );
  }

  @override
  State<SimplePWADialog> createState() => _SimplePWADialogState();
}

class _SimplePWADialogState extends State<SimplePWADialog>
    with SingleTickerProviderStateMixin {
  final SimplePWAService _pwaService = SimplePWAService();
  bool _isInstalling = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _handleInstall() async {
    if (_isInstalling) return;

    setState(() {
      _isInstalling = true;
    });

    try {
      final success = await _pwaService.promptInstall();
      
      if (success) {
        // Brief delay for user feedback
        await Future.delayed(const Duration(milliseconds: 300));
        
        if (mounted) {
          Navigator.of(context).pop();
          widget.onInstallSuccess?.call();
        }
      } else {
        setState(() {
          _isInstalling = false;
        });
      }
    } catch (e) {
      debugPrint('❌ Error during PWA installation: $e');
      setState(() {
        _isInstalling = false;
      });
    }
  }

  void _handleDismiss() {
    _pwaService.markPromptShown();
    Navigator.of(context).pop();
    widget.onDismiss?.call();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          margin: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: theme.scaffoldBackgroundColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(theme, isDarkMode),
              _buildContent(theme),
              _buildActions(theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: KFTDesignSystem.primaryColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.download_rounded,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Install KFT Fitness',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Get quick access to your workouts',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Install this app on your device for:',
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildBenefit(theme, Icons.flash_on, 'Faster access'),
          const SizedBox(height: 12),
          _buildBenefit(theme, Icons.offline_bolt, 'Works offline'),
          const SizedBox(height: 12),
          _buildBenefit(theme, Icons.home_rounded, 'Home screen shortcut'),
        ],
      ),
    );
  }

  Widget _buildBenefit(ThemeData theme, IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: KFTDesignSystem.primaryColor,
        ),
        const SizedBox(width: 12),
        Text(
          text,
          style: theme.textTheme.bodyMedium,
        ),
      ],
    );
  }

  Widget _buildActions(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: _isInstalling ? null : _handleDismiss,
              child: Text(
                'Not now',
                style: TextStyle(
                  color: theme.hintColor,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _isInstalling ? null : _handleInstall,
              style: ElevatedButton.styleFrom(
                backgroundColor: KFTDesignSystem.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isInstalling
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'Install',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
