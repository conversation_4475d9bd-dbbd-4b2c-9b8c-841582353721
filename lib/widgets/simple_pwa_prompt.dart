import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/simple_pwa_service.dart';
import 'simple_pwa_dialog.dart';
import 'dart:html' as html;

/// Simple PWA prompt widget that shows install dialog once on page load
/// Provides a clean, professional approach to PWA installation
class SimplePWAPrompt extends StatefulWidget {
  final Widget child;

  const SimplePWAPrompt({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<SimplePWAPrompt> createState() => _SimplePWAPromptState();
}

class _SimplePWAPromptState extends State<SimplePWAPrompt> {
  final SimplePWAService _pwaService = SimplePWAService();

  @override
  void initState() {
    super.initState();
    if (kIsWeb) {
      _pwaService.initialize();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (kIsWeb)
          ValueListenableBuilder<bool>(
            valueListenable: _pwaService.installPromptAvailable,
            builder: (context, available, _) {
              debugPrint('🔔 PWA installPromptAvailable: ' + available.toString());
              if (!available) return const SizedBox.shrink();
              return Positioned(
                bottom: 32,
                right: 32,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.download_rounded),
                  label: const Text('Install App'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 6,
                  ),
                  onPressed: () async {
                    debugPrint('🟢 Install button pressed');
                    await _pwaService.promptInstall();
                  },
                ),
              );
            },
          ),
        // Fallback manual button for testing
        if (kIsWeb)
          Positioned(
            bottom: 100,
            right: 32,
            child: ElevatedButton(
              child: const Text('Force Install (Test)'),
              onPressed: () async {
                debugPrint('🟠 Force Install button pressed');
                await _pwaService.promptInstall();
              },
            ),
          ),
      ],
    );
  }
}
