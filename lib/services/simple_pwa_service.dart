import 'dart:async';
import 'dart:html' as html;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Enhanced PWA installation service
/// Provides comprehensive PWA installation detection and persistent prompt management
class SimplePWAService {
  static final SimplePWAService _instance = SimplePWAService._internal();
  factory SimplePWAService() => _instance;
  SimplePWAService._internal();

  // Event storage
  dynamic _beforeInstallPromptEvent;

  // State tracking
  bool _isInstalled = false;
  bool _isInstallable = false;
  bool _hasShownPrompt = false;
  bool _userDismissedPermanently = false;
  int _promptDismissCount = 0;
  DateTime? _lastPromptTime;

  // Configuration
  static const int maxDismissCount = 3; // Show prompt up to 3 times
  static const Duration promptCooldown = Duration(hours: 24); // Wait 24h between prompts
  static const String _dismissCountKey = 'pwa_dismiss_count';
  static const String _lastPromptKey = 'pwa_last_prompt';
  static const String _permanentDismissKey = 'pwa_permanent_dismiss';

  // Notifiers
  final ValueNotifier<bool> installPromptAvailable = ValueNotifier<bool>(false);
  final ValueNotifier<bool> shouldShowDialog = ValueNotifier<bool>(false);

  // Stream controllers
  final StreamController<bool> _installabilityController = StreamController<bool>.broadcast();
  final StreamController<bool> _installationController = StreamController<bool>.broadcast();

  /// Stream that emits when PWA installability changes
  Stream<bool> get installabilityStream => _installabilityController.stream;

  /// Stream that emits when PWA installation status changes
  Stream<bool> get installationStream => _installationController.stream;

  /// Whether the PWA is currently installable
  bool get isInstallable => _isInstallable;

  /// Whether the PWA is already installed
  bool get isInstalled => _isInstalled;

  /// Whether we should show the install prompt
  bool get shouldShowPrompt => _isInstallable && !_isInstalled && !_userDismissedPermanently && _canShowPrompt();

  /// Initialize the PWA service
  Future<void> initialize() async {
    if (!kIsWeb) return;

    try {
      debugPrint('🔧 Initializing Enhanced PWA Service...');

      // Load persistent state
      await _loadPersistentState();

      // Check if already installed
      _checkInstallationStatus();

      // Set up event listeners
      _setupEventListeners();

      // Check if we should show dialog on startup
      _checkShouldShowDialog();

      debugPrint('✅ Enhanced PWA Service initialized');
    } catch (e) {
      debugPrint('❌ Error initializing Enhanced PWA Service: $e');
    }
  }

  /// Load persistent state from SharedPreferences
  Future<void> _loadPersistentState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _promptDismissCount = prefs.getInt(_dismissCountKey) ?? 0;
      _userDismissedPermanently = prefs.getBool(_permanentDismissKey) ?? false;

      final lastPromptMillis = prefs.getInt(_lastPromptKey);
      if (lastPromptMillis != null) {
        _lastPromptTime = DateTime.fromMillisecondsSinceEpoch(lastPromptMillis);
      }

      debugPrint('📊 PWA State loaded - Dismiss count: $_promptDismissCount, Permanent dismiss: $_userDismissedPermanently');
    } catch (e) {
      debugPrint('❌ Error loading PWA persistent state: $e');
    }
  }

  /// Save persistent state to SharedPreferences
  Future<void> _savePersistentState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_dismissCountKey, _promptDismissCount);
      await prefs.setBool(_permanentDismissKey, _userDismissedPermanently);

      if (_lastPromptTime != null) {
        await prefs.setInt(_lastPromptKey, _lastPromptTime!.millisecondsSinceEpoch);
      }
    } catch (e) {
      debugPrint('❌ Error saving PWA persistent state: $e');
    }
  }

  /// Check if we can show a prompt based on cooldown and dismiss count
  bool _canShowPrompt() {
    if (_promptDismissCount >= maxDismissCount) {
      return false;
    }

    if (_lastPromptTime != null) {
      final timeSinceLastPrompt = DateTime.now().difference(_lastPromptTime!);
      if (timeSinceLastPrompt < promptCooldown) {
        return false;
      }
    }

    return true;
  }

  /// Check if we should show the dialog on startup
  void _checkShouldShowDialog() {
    // Show dialog if installable and should show prompt
    if (shouldShowPrompt) {
      // Delay to ensure UI is ready
      Future.delayed(const Duration(milliseconds: 1500), () {
        if (!_isInstalled && _isInstallable && !_userDismissedPermanently) {
          shouldShowDialog.value = true;
          debugPrint('🔔 PWA dialog should be shown on startup');
        }
      });
    }
  }

  /// Check if the app is already installed
  void _checkInstallationStatus() {
    try {
      // Check multiple indicators for installation
      final standaloneQuery = html.window.matchMedia('(display-mode: standalone)');
      final fullscreenQuery = html.window.matchMedia('(display-mode: fullscreen)');
      final minimalUiQuery = html.window.matchMedia('(display-mode: minimal-ui)');

      // Check if running in any PWA mode
      _isInstalled = standaloneQuery.matches || fullscreenQuery.matches || minimalUiQuery.matches;

      // Also check for iOS Safari standalone mode
      if (!_isInstalled) {
        try {
          final navigator = html.window.navigator;
          final isIOS = navigator.userAgent.contains('iPhone') || navigator.userAgent.contains('iPad');
          if (isIOS) {
            // Check for iOS standalone mode using JS interop
            final isStandalone = html.window.navigator.userAgent.contains('standalone');
            _isInstalled = isStandalone;
          }
        } catch (e) {
          debugPrint('⚠️ Could not check iOS standalone mode: $e');
        }
      }

      if (_isInstalled) {
        debugPrint('📱 PWA is already installed (standalone mode detected)');
        installPromptAvailable.value = false;
        shouldShowDialog.value = false;
        _installationController.add(true);
      } else {
        debugPrint('🌐 PWA is running in browser mode');
      }
    } catch (e) {
      debugPrint('❌ Error checking installation status: $e');
    }
  }

  /// Set up PWA event listeners
  void _setupEventListeners() {
    try {
      // Listen for beforeinstallprompt event
      html.window.addEventListener('beforeinstallprompt', (event) {
        debugPrint('📱 PWA beforeinstallprompt event received');
        event.preventDefault();

        if (!_isInstalled) {
          _beforeInstallPromptEvent = event;
          _isInstallable = true;
          installPromptAvailable.value = shouldShowPrompt;
          _installabilityController.add(_isInstallable);

          // Check if we should show dialog
          _checkShouldShowDialog();

          debugPrint('✅ PWA is installable, prompt available: ${shouldShowPrompt}');
        }
      });

      // Listen for appinstalled event
      html.window.addEventListener('appinstalled', (event) {
        debugPrint('🎉 PWA installed successfully');
        _isInstalled = true;
        _isInstallable = false;
        _beforeInstallPromptEvent = null;
        installPromptAvailable.value = false;
        shouldShowDialog.value = false;
        _installabilityController.add(false);
        _installationController.add(true);

        // Reset dismiss state since app is now installed
        _resetDismissState();
      });

      // Listen for display mode changes (for better installation detection)
      try {
        final standaloneQuery = html.window.matchMedia('(display-mode: standalone)');
        standaloneQuery.addEventListener('change', (event) {
          _checkInstallationStatus();
        });
      } catch (e) {
        debugPrint('⚠️ Could not set up display mode listener: $e');
      }

      debugPrint('✅ Enhanced PWA event listeners set up');
    } catch (e) {
      debugPrint('❌ Error setting up PWA event listeners: $e');
    }
  }

  /// Reset dismiss state when app is installed
  Future<void> _resetDismissState() async {
    _promptDismissCount = 0;
    _userDismissedPermanently = false;
    _lastPromptTime = null;
    await _savePersistentState();
  }

  /// Prompt the user to install the PWA
  Future<bool> promptInstall() async {
    if (!kIsWeb || _isInstalled || _beforeInstallPromptEvent == null) {
      debugPrint('⚠️ Cannot prompt install - not available (installed: $_isInstalled, event: ${_beforeInstallPromptEvent != null})');
      return false;
    }

    try {
      debugPrint('📱 Prompting user to install PWA...');

      // Record that we're showing a prompt
      _lastPromptTime = DateTime.now();
      await _savePersistentState();

      final dynamic prompt = _beforeInstallPromptEvent;
      final result = await prompt.prompt();

      if (result.outcome == 'accepted') {
        debugPrint('🎉 User accepted PWA installation');
        // Don't reset the event here - let appinstalled event handle it
        return true;
      } else {
        debugPrint('❌ User declined PWA installation');
        await _handlePromptDismiss();
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error prompting PWA install: $e');
      await _handlePromptDismiss();
      return false;
    }
  }

  /// Handle when user dismisses the prompt
  Future<void> _handlePromptDismiss() async {
    _promptDismissCount++;

    if (_promptDismissCount >= maxDismissCount) {
      _userDismissedPermanently = true;
      debugPrint('🚫 User has dismissed PWA prompt $_promptDismissCount times - marking as permanent dismiss');
    }

    await _savePersistentState();
    installPromptAvailable.value = false;
    shouldShowDialog.value = false;
  }

  /// Mark that the dialog prompt has been shown (but not necessarily dismissed)
  void markDialogShown() {
    shouldShowDialog.value = false;
    debugPrint('📋 PWA dialog marked as shown');
  }

  /// Mark that the prompt has been shown (legacy method for compatibility)
  void markPromptShown() {
    markDialogShown();
  }

  /// Force show the install dialog (for testing or manual triggers)
  void forceShowDialog() {
    if (_isInstallable && !_isInstalled) {
      shouldShowDialog.value = true;
      debugPrint('🔧 Forcing PWA dialog to show');
    }
  }

  /// Reset all PWA state (for testing purposes)
  Future<void> resetState() async {
    _promptDismissCount = 0;
    _userDismissedPermanently = false;
    _lastPromptTime = null;
    _hasShownPrompt = false;
    await _savePersistentState();

    // Re-check if we should show prompts
    if (_isInstallable && !_isInstalled) {
      installPromptAvailable.value = true;
      shouldShowDialog.value = true;
    }

    debugPrint('🔄 PWA state reset');
  }

  /// Dispose of resources
  void dispose() {
    _installabilityController.close();
    _installationController.close();
    installPromptAvailable.dispose();
    shouldShowDialog.dispose();
  }
}
