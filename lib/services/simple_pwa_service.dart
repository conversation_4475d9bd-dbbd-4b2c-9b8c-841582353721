import 'dart:async';
import 'dart:html' as html;
import 'package:flutter/foundation.dart';
import 'package:flutter/foundation.dart';

/// Simple PWA installation service
/// Provides basic PWA installation detection and prompt triggering
class SimplePWAService {
  static final SimplePWAService _instance = SimplePWAService._internal();
  factory SimplePWAService() => _instance;
  SimplePWAService._internal();

  // Event storage
  dynamic _beforeInstallPromptEvent;
  
  // State tracking
  bool _isInstalled = false;
  bool _isInstallable = false;
  bool _hasShownPrompt = false;
  
  // Notifier for install button
  final ValueNotifier<bool> installPromptAvailable = ValueNotifier<bool>(false);
  
  // Stream controllers
  final StreamController<bool> _installabilityController = StreamController<bool>.broadcast();
  
  /// Stream that emits when PWA installability changes
  Stream<bool> get installabilityStream => _installabilityController.stream;
  
  /// Whether the PWA is currently installable
  bool get isInstallable => _isInstallable;
  
  /// Whether the PWA is already installed
  bool get isInstalled => _isInstalled;
  
  /// Whether we should show the install prompt
  bool get shouldShowPrompt => _isInstallable && !_isInstalled && !_hasShownPrompt;

  /// Initialize the PWA service
  Future<void> initialize() async {
    if (!kIsWeb) return;
    
    try {
      debugPrint('🔧 Initializing Simple PWA Service...');
      
      // Check if already installed
      _checkInstallationStatus();
      
      // Set up event listeners
      _setupEventListeners();
      
      debugPrint('✅ Simple PWA Service initialized');
    } catch (e) {
      debugPrint('❌ Error initializing Simple PWA Service: $e');
    }
  }

  /// Check if the app is already installed
  void _checkInstallationStatus() {
    try {
      // Check if running in standalone mode
      final mediaQuery = html.window.matchMedia('(display-mode: standalone)');
      _isInstalled = mediaQuery.matches;
      
      if (_isInstalled) {
        debugPrint('📱 PWA is already installed');
      }
    } catch (e) {
      debugPrint('❌ Error checking installation status: $e');
    }
  }

  /// Set up PWA event listeners
  void _setupEventListeners() {
    try {
      // Listen for beforeinstallprompt event
      html.window.addEventListener('beforeinstallprompt', (event) {
        debugPrint('📱 PWA beforeinstallprompt event received');
        event.preventDefault();
        if (_beforeInstallPromptEvent == null) {
          _beforeInstallPromptEvent = event;
          _isInstallable = true;
          installPromptAvailable.value = true;
          _installabilityController.add(_isInstallable);
        }
      });

      // Listen for appinstalled event
      html.window.addEventListener('appinstalled', (event) {
        debugPrint('🎉 PWA installed successfully');
        _isInstalled = true;
        _isInstallable = false;
        _beforeInstallPromptEvent = null;
        installPromptAvailable.value = false;
        _installabilityController.add(_isInstallable);
      });

      debugPrint('✅ PWA event listeners set up');
    } catch (e) {
      debugPrint('❌ Error setting up PWA event listeners: $e');
    }
  }

  /// Prompt the user to install the PWA
  Future<bool> promptInstall() async {
    if (!kIsWeb || _isInstalled || _beforeInstallPromptEvent == null) {
      debugPrint('⚠️ Cannot prompt install - not available');
      return false;
    }

    try {
      debugPrint('📱 Prompting user to install PWA...');
      
      final dynamic prompt = _beforeInstallPromptEvent;
      final result = await prompt.prompt();
      
      _beforeInstallPromptEvent = null;
      installPromptAvailable.value = false;
      
      if (result.outcome == 'accepted') {
        debugPrint('🎉 User accepted PWA installation');
        return true;
      } else {
        debugPrint('❌ User declined PWA installation');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error prompting PWA install: $e');
      _beforeInstallPromptEvent = null;
      installPromptAvailable.value = false;
      return false;
    }
  }

  /// Mark that the prompt has been shown
  void markPromptShown() {
    _hasShownPrompt = true;
    installPromptAvailable.value = false;
  }

  /// Dispose of resources
  void dispose() {
    _installabilityController.close();
    installPromptAvailable.dispose();
  }
}
