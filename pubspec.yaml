name: kft
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.7+14

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_riverpod: ^2.4.0
  flutter_secure_storage: ^9.0.0
  flutter_advanced_segment: ^3.1.0

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # For persistent storage
  shared_preferences: ^2.2.2

  # For charts
  fl_chart: ^0.66.0

  # For date formatting
  intl: ^0.20.2

  # For HTTP requests
  http: ^1.1.0

  # For launching URLs (WhatsApp)
  url_launcher: ^6.3.1

  # For WebView (Vimeo video player)
  webview_flutter: ^4.7.0

  # For device information
  device_info_plus: ^9.1.2

  # For screen protection
  screen_protector: ^1.4.2+1

  # WebSocket support removed

  google_fonts: ^6.1.0

  flutter_slidable: ^3.0.0

  # For in-app Vimeo video playback
  vimeo_video_player: ^1.0.1

  # For improved video player functionality
  video_player: ^2.8.2
  chewie: ^1.8.5

  # For security and encryption
  crypto: ^3.0.3

  image_picker: ^1.0.7

  provider: ^6.1.2
  share_plus: ^7.2.1
  path_provider: ^2.1.2

  font_awesome_flutter: ^10.7.0

  # Firebase Cloud Messaging for reliable notifications
  firebase_core: ^3.6.0
  firebase_analytics: ^11.3.3

  # Enhanced local notifications with better features
  awesome_notifications: ^0.10.1

  # For timezone support
  timezone: ^0.9.4

  # For permission handling
  permission_handler: ^12.0.0+1

  # For Android intents (battery optimization)
  android_intent_plus: ^4.0.3

  # For opening app settings
  app_settings: ^5.1.1

  # For keeping screen awake during video playback
  wakelock_plus: ^1.2.8

  # For connectivity monitoring
  connectivity_plus: ^6.0.5

  encrypt: ^5.0.3

  # Additional dependencies for bulletproof stability
  sqflite: ^2.3.3+1
  path: ^1.9.0
  stack_trace: ^1.11.1

  cached_network_image: ^3.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # For generating launcher icons
  flutter_launcher_icons: ^0.13.1



# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

  assets:
    - assets/images/logo.webp
    - hosted_vimeo_player.html

# Configuration for flutter_launcher_icons
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/kft_launcher_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/images/kft_launcher_icon.png"
    background_color: "#3D5AFE"
    theme_color: "#3D5AFE"
  windows:
    generate: true
    image_path: "assets/images/kft_launcher_icon.png"
    icon_size: 48
  macos:


